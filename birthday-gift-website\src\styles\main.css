/* ===== 重置和基础样式 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  overflow-x: hidden;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* ===== 布局容器 ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.theme-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  max-width: 600px;
  margin: 0 auto;
}

.main {
  flex: 1;
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

/* ===== 主题按钮样式 ===== */
.theme-btn {
  padding: 0.75rem 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-width: 100px;
  min-height: 44px; /* 移动端友好的最小点击区域 */
}

.theme-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.theme-btn.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

.theme-btn:active {
  transform: translateY(0);
}

/* ===== 背景和内容样式 ===== */
.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: opacity 0.5s ease;
}

.content-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  z-index: 2;
  padding: 2rem;
}

.text-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
  max-width: 50%;
  margin-right: 5%;
}

.blessing-text {
  font-size: clamp(1.5rem, 6vw, 2.5rem);
  font-weight: 700;
  color: white;
  text-shadow:
    0 2px 4px rgba(0, 0, 0, 0.5),
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 8px 16px rgba(0, 0, 0, 0.2);
  margin: 0 0 2rem 0;
  line-height: 1.2;
  letter-spacing: 0.05em;
  animation: fadeInUp 1s ease-out;
}

.poem-container {
  margin-top: 1rem;
  transition: opacity 0.5s ease;
}

.poem-text {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.poem-line {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: 400;
  color: rgba(255, 255, 255, 0.95);
  text-shadow:
    0 1px 2px rgba(0, 0, 0, 0.7),
    0 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.4;
  letter-spacing: 0.01em;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.poem-line.poem-break {
  height: 0.8rem;
  opacity: 0;
}

/* ===== 音频播放器样式 ===== */
.audio-player {
  display: flex;
  align-items: center;
  gap: 1rem; /* 减少间距，为进度条腾出更多空间 */
  position: relative;
  width: 100%;
  min-height: 120px;
  /* 确保在小屏幕上也能正常显示 */
  flex-wrap: nowrap;
  overflow: hidden;
  padding: 0 2rem; /* 增加左右内边距，确保内容不贴边 */
}

/* 唱片区域：左侧 */
.vinyl-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.vinyl-container {
  position: relative;
  width: 70px; /* 减小唱片尺寸，为进度条腾出空间 */
  height: 70px;
  flex-shrink: 0;
}

/* 播放控制区域：中间 */
.control-section {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}



/* 进度条区域：右侧，包含歌词、进度条、时间 */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1; /* 让进度条区域占据所有剩余空间 */
  min-width: 0; /* 移除最小宽度限制 */
  /* 移除最大宽度限制，让进度条可以充分利用空间 */
  align-items: center;
}

.current-lyric {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.2rem;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
  transition: all 0.3s ease;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  margin-bottom: 0.25rem;
  width: 100%;
  /* 限制歌词高度，防止影响布局 */
  max-height: 2.8rem; /* 约2行文字的高度 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.progress-container {
  width: 100%;
  display: flex;
  align-items: center;
  /* 移除最小宽度限制，让进度条可以充分利用父容器的空间 */
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.25);
  border-radius: 3px;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  cursor: pointer;
  transition: height 0.2s ease, box-shadow 0.2s ease;
}

.progress-bar:hover {
  height: 8px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2), 0 0 8px rgba(255, 255, 255, 0.3);
}

.progress-bar:active {
  height: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 3px;
  width: 0%;
  transition: width 0.1s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 1px;
  box-shadow: 0 0 4px rgba(255, 255, 255, 0.5);
}

/* 进度条focus和触摸优化 */
.progress-bar:focus,
.progress-bar:focus-visible,
.progress-bar:active,
.progress-container:focus,
.progress-container:focus-visible {
  outline: none;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 播放控制按钮 */
.play-control-btn {
  width: 36px; /* 减小按钮尺寸 */
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  font-size: 14px; /* 减小字体尺寸 */
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s ease;
  flex-shrink: 0; /* 确保按钮不会被压缩 */
}

.play-control-btn:active {
  background: rgba(255, 255, 255, 0.8);
}

/* 播放和暂停图标样式统一 */
.play-icon,
.pause-icon {
  color: #333 !important;
  font-size: inherit;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: system-ui, -apple-system, sans-serif;
  font-weight: normal;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.time-display {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.85);
  white-space: nowrap;
  text-align: center;
  font-weight: 500;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  width: 100%;
  /* 确保时间显示不会被压缩 */
  flex-shrink: 0;
}

/* 响应式设计：小屏幕优化 */
@media (max-width: 768px) {
  .audio-player {
    gap: 0.8rem;
    min-height: 100px;
    padding: 0 1rem; /* 保持合理的内边距 */
  }

  .progress-section {
    /* 移除所有宽度限制，让进度条充分利用空间 */
  }

  .current-lyric {
    font-size: 1rem;
    max-height: 2.4rem;
  }

  .vinyl-container {
    width: 60px;
    height: 60px;
  }

  .play-control-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }


}

@media (max-width: 480px) {
  .audio-player {
    gap: 0.5rem;
    flex-direction: column;
    min-height: auto;
    padding: 1rem;
  }

  .progress-section {
    /* 移除所有宽度限制，让进度条在小屏幕上也能充分利用空间 */
    order: -1; /* 将进度条区域移到顶部 */
    width: 100%; /* 确保占据全宽 */
  }
}

  .vinyl-section,
  .control-section {
    flex-direction: row;
    gap: 1rem;
  }

  .vinyl-container {
    width: 50px;
    height: 50px;
  }

  .play-control-btn {
    width: 30px;
    height: 30px;
    font-size: 10px;
  }


}

.vinyl-record {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  background: linear-gradient(45deg, #1a1a1a, #333);
  box-shadow:
    0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 2px 4px rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease;
}

/* 移除了专辑图片的悬停缩放效果，避免干扰播放动画 */

.vinyl-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  object-position: center center; /* 确保图片居中显示 */
  transition: opacity 0.3s ease-in-out;
  /* 确保图片始终保持圆形，即使原图不是正方形 */
  aspect-ratio: 1 / 1; /* 强制保持1:1的宽高比 */
}



/* ===== 响应式设计 ===== */
/* 平板设备 */
@media (max-width: 768px) {
  .header {
    padding: 0.75rem;
  }

  .theme-buttons {
    gap: 0.75rem;
  }

  .theme-btn {
    padding: 0.8rem 1.6rem;
    font-size: 0.95rem;
    min-width: 100px;
    white-space: nowrap; /* 防止文字换行 */
  }

  .content-overlay {
    padding: 1.5rem;
    justify-content: center;
  }

  .text-content {
    max-width: 70%;
    margin-right: 0;
    align-items: center;
    text-align: center;
  }

  .blessing-text {
    font-size: clamp(1.2rem, 5vw, 2rem);
    margin-bottom: 1.5rem;
  }

  .poem-line {
    font-size: clamp(0.9rem, 2.5vw, 1.2rem);
    white-space: normal;
  }

}

/* 手机设备 */
@media (max-width: 480px) {
  .header {
    padding: 0.5rem;
  }

  .theme-buttons {
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .theme-btn {
    padding: 0.7rem 1.4rem;
    font-size: 0.9rem;
    min-width: 90px;
    flex: 1;
    max-width: 140px;
    white-space: nowrap; /* 防止文字换行 */
  }

  .content-overlay {
    padding: 1rem;
    justify-content: flex-end;
  }

  .text-content {
    max-width: 90%;
    margin-right: 5%;
    align-items: flex-end;
    text-align: right;
  }

  .blessing-text {
    font-size: clamp(1.2rem, 8vw, 2.5rem);
    margin-bottom: 1rem;
  }

  .poem-line {
    font-size: clamp(0.8rem, 4vw, 1.1rem);
    white-space: normal;
    line-height: 1.5;
  }

  .poem-text {
    gap: 0.3rem;
  }

  /* 主题3特殊布局 - 文字显示在顶部按钮区下方 */
  [data-theme="2"] .content-overlay {
    justify-content: center;
    align-items: flex-start;
    padding-top: 6rem;
  }

  [data-theme="2"] .text-content {
    max-width: 80%;
    margin-right: 0;
    align-items: center;
    text-align: center;
  }

  .footer {
    padding: 1rem;
    /* 移动端让底部播放区域完全融入页面 */
    background: transparent;
    backdrop-filter: none;
  }

  /* 移动端音频播放器垂直布局 */
  .audio-player {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .vinyl-section {
    justify-content: center;
  }

  .control-section {
    justify-content: center;
  }

  .progress-section {
    align-items: center;
  }

  .current-lyric {
    font-size: 1rem;
  }

  .play-control-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }


}

/* 大屏设备优化 */
@media (min-width: 1200px) {
  .theme-btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
  }

  .vinyl-container {
    width: 120px;
    height: 120px;
  }

  .current-lyric {
    font-size: 1.4rem;
    font-weight: 600;
  }

  .time-display {
    font-size: 0.9rem;
  }

  .progress-container {
    height: 8px;
  }


}

/* ===== 基础动画 ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== 无障碍和性能优化 ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .theme-btn {
    border-width: 3px;
  }

  .blessing-text {
    text-shadow:
      0 0 0 black,
      0 0 0 black,
      2px 2px 0 black;
  }
}

/* ===== 移动端专用优化 ===== */

/* 防止iOS Safari的橡皮筋效果 */
body {
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

/* 移动端触摸优化 */
.theme-btn,
.play-control-btn,
.vinyl-record,
.progress-bar,
.progress-container,
.progress-fill {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  touch-action: manipulation;
}

/* 移动端按钮按下效果 */
.theme-btn:active,
.play-control-btn:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* iOS Safari地址栏适配 */
@supports (-webkit-touch-callout: none) {
  .main {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }

  .header {
    padding-top: env(safe-area-inset-top);
  }

  .footer {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .header {
    padding: 0.5rem;
  }

  .theme-buttons {
    gap: 0.5rem;
  }

  .theme-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    min-height: 36px;
  }

  .blessing-text {
    font-size: clamp(1.2rem, 6vw, 2.5rem);
  }

  .footer {
    padding: 0.75rem;
    /* 横屏模式下移动端让底部区域完全融入页面 */
    background: transparent;
    backdrop-filter: none;
  }

  /* 横屏模式音频播放器垂直布局 */
  .audio-player {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* 超小屏幕设备 */
@media (max-width: 320px) {
  .theme-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
    min-width: 60px;
  }

  .blessing-text {
    font-size: clamp(1.2rem, 12vw, 2.5rem);
    padding: 0.5rem;
  }

}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .background-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 移动端性能优化 */
@media (max-width: 768px) {
  /* 减少动画复杂度 */
  .theme-decoration {
    display: none;
  }

  /* 简化阴影效果 */
  .theme-btn {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .theme-btn.active {
    box-shadow: 0 2px 12px rgba(255, 255, 255, 0.3);
  }

  /* 优化文字渲染 */
  .blessing-text {
    text-shadow:
      0 1px 2px rgba(0, 0, 0, 0.8),
      0 2px 4px rgba(0, 0, 0, 0.4);
  }

  /* 移动端让底部播放区域完全融入页面 */
  .footer {
    background: transparent;
    backdrop-filter: none;
  }


}

/* 触摸设备特定样式 */
@media (pointer: coarse) {
  .theme-btn {
    min-height: 48px; /* 更大的触摸目标 */
    min-width: 48px;
  }



  /* 移除hover效果，避免触摸设备的粘滞 */
  .theme-btn:hover,
  .play-control-btn:hover,
  .vinyl-record:hover,
  .progress-bar:hover {
    transform: none;
    animation: none;
  }


}

/* ===== 移动端特殊模式样式 ===== */

/* 横屏模式 */
.landscape-mode .header {
  padding: 0.5rem 1rem;
}

.landscape-mode .footer {
  padding: 0.75rem 1rem;
}

.landscape-mode .blessing-text {
  font-size: clamp(1.5rem, 5vw, 2.5rem);
}

/* 低带宽模式 */
.low-bandwidth .background-image {
  filter: blur(1px); /* 轻微模糊减少带宽 */
}

.low-bandwidth .theme-decoration {
  display: none;
}

.low-bandwidth .vinyl-record {
  animation-duration: 5s !important; /* 减慢动画 */
}

/* 省电模式 */
.power-save * {
  animation-duration: 0.1s !important;
  transition-duration: 0.1s !important;
}

.power-save .theme-decoration {
  display: none;
}

.power-save .vinyl-record.playing {
  animation: none; /* 完全禁用旋转动画 */
}

/* iOS特定样式 */
.ios-audio-prompt .prompt-content {
  background: rgba(255, 255, 255, 0.95);
  padding: 2rem;
  border-radius: 10px;
  text-align: center;
  color: #333;
  backdrop-filter: blur(10px);
}

.ios-audio-prompt button {
  margin-top: 1rem;
  padding: 0.75rem 1.5rem;
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
}

/* 脉冲动画（用于提示用户交互） */
.pulse-animation {
  animation: pulsePrompt 1s ease-in-out infinite;
}

@keyframes pulsePrompt {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
  }
}

/* 使用CSS变量适配iOS Safari */
.main {
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
}

/* 防止iOS橡皮筋滚动 - 使用更温和的方式 */
html, body {
  width: 100%;
  height: 100%;
  overscroll-behavior: none; /* 防止橡皮筋效果 */
}

.main {
  position: relative;
  overflow: hidden;
}

/* 确保内容在安全区域内 */
@supports (padding: max(0px)) {
  .header {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  .footer {
    padding-left: max(1.5rem, env(safe-area-inset-left));
    padding-right: max(1.5rem, env(safe-area-inset-right));
    padding-bottom: max(1.5rem, env(safe-area-inset-bottom));
  }
}

/* 优化小屏幕的可读性 */
@media (max-height: 600px) {
  .blessing-text {
    font-size: clamp(1rem, 8vw, 2rem);
    line-height: 1.1;
  }

  .header {
    padding: 0.5rem;
  }

  .footer {
    padding: 1rem;
    /* 小屏幕高度下移动端让底部区域完全融入页面 */
    background: transparent;
    backdrop-filter: none;
  }
}
